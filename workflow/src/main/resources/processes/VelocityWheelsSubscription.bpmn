<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" 
             xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" 
             xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" 
             xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" 
             xmlns:flowable="http://flowable.org/bpmn" 
             xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
             typeLanguage="http://www.w3.org/2001/XMLSchema" 
             expressionLanguage="http://www.w3.org/1999/XPath" 
             targetNamespace="http://www.flowable.org/processdef">
  
  <message id="pauseSubscription" name="pauseSubscription"/>
  <message id="resumeSubscription" name="resumeSubscription"/>
  <message id="cancelSubscription" name="cancelSubscription"/>
  <message id="processSubscriptionBilling" name="processSubscriptionBilling"/>
  
  <process id="velocityWheelsSubscription" name="Velocity Wheels Subscription" isExecutable="true">
    
    <!-- Start Event -->
    <startEvent id="startEvent" name="Start"/>
    
    <!-- Create Subscription -->
    <serviceTask id="createSubscription" name="Create Subscription" flowable:delegateExpression="${createSubscription}"/>
    <sequenceFlow id="flow1" sourceRef="startEvent" targetRef="createSubscription"/>
    
    <!-- Activate Subscription -->
    <serviceTask id="activateSubscription" name="Activate Subscription" flowable:delegateExpression="${activateSubscription}"/>
    <sequenceFlow id="flow2" sourceRef="createSubscription" targetRef="activateSubscription"/>
    
    <!-- Billing Process Subprocess -->
    <subProcess id="billingProcess" name="Billing Process">
      <startEvent id="billingStart" name="Start Billing"/>
      
      <!-- Process Billing -->
      <serviceTask id="processSubscriptionBilling" name="Process Subscription Billing" flowable:async="true" flowable:delegateExpression="${processSubscriptionBilling}"/>
      <sequenceFlow id="billingFlow1" sourceRef="billingStart" targetRef="processSubscriptionBilling"/>
      
      <!-- Create Invoice -->
      <serviceTask id="createSubscriptionInvoice" name="Create Subscription Invoice" flowable:async="true" flowable:delegateExpression="${createSubscriptionInvoice}"/>
      <sequenceFlow id="billingFlow2" sourceRef="processSubscriptionBilling" targetRef="createSubscriptionInvoice"/>
      
      <!-- Send Invoice -->
      <serviceTask id="sendSubscriptionInvoice" name="Send Subscription Invoice" flowable:async="true" flowable:delegateExpression="${sendSubscriptionInvoice}"/>
      <sequenceFlow id="billingFlow3" sourceRef="createSubscriptionInvoice" targetRef="sendSubscriptionInvoice"/>
      
      <endEvent id="billingEnd" name="End Billing"/>
      <sequenceFlow id="billingFlow4" sourceRef="sendSubscriptionInvoice" targetRef="billingEnd"/>
    </subProcess>
    
    <sequenceFlow id="flow3" sourceRef="activateSubscription" targetRef="billingProcess"/>
    
    <!-- Wait for Events -->
    <intermediateCatchEvent id="waitForEvents" name="Wait for Events">
      <messageEventDefinition messageRef="processSubscriptionBilling"/>
    </intermediateCatchEvent>
    <sequenceFlow id="flow4" sourceRef="billingProcess" targetRef="waitForEvents"/>
    
    <!-- Gateway for different events -->
    <eventBasedGateway id="eventGateway" name="Event Gateway"/>
    <sequenceFlow id="flow5" sourceRef="waitForEvents" targetRef="eventGateway"/>
    
    <!-- Billing Event -->
    <intermediateCatchEvent id="billingEvent" name="Billing Event">
      <messageEventDefinition messageRef="processSubscriptionBilling"/>
    </intermediateCatchEvent>
    <sequenceFlow id="flow6" sourceRef="eventGateway" targetRef="billingEvent"/>
    <sequenceFlow id="flow7" sourceRef="billingEvent" targetRef="billingProcess"/>
    
    <!-- Pause Event -->
    <intermediateCatchEvent id="pauseEvent" name="Pause Event">
      <messageEventDefinition messageRef="pauseSubscription"/>
    </intermediateCatchEvent>
    <sequenceFlow id="flow8" sourceRef="eventGateway" targetRef="pauseEvent"/>
    
    <!-- Pause Task -->
    <serviceTask id="pauseSubscription" name="Pause Subscription" flowable:delegateExpression="${pauseSubscription}"/>
    <sequenceFlow id="flow9" sourceRef="pauseEvent" targetRef="pauseSubscription"/>
    
    <!-- Resume Event -->
    <intermediateCatchEvent id="resumeEvent" name="Resume Event">
      <messageEventDefinition messageRef="resumeSubscription"/>
    </intermediateCatchEvent>
    <sequenceFlow id="flow10" sourceRef="pauseSubscription" targetRef="resumeEvent"/>
    
    <!-- Resume Task -->
    <serviceTask id="resumeSubscription" name="Resume Subscription" flowable:delegateExpression="${resumeSubscription}"/>
    <sequenceFlow id="flow11" sourceRef="resumeEvent" targetRef="resumeSubscription"/>
    <sequenceFlow id="flow12" sourceRef="resumeSubscription" targetRef="waitForEvents"/>
    
    <!-- Cancel Event -->
    <intermediateCatchEvent id="cancelEvent" name="Cancel Event">
      <messageEventDefinition messageRef="cancelSubscription"/>
    </intermediateCatchEvent>
    <sequenceFlow id="flow13" sourceRef="eventGateway" targetRef="cancelEvent"/>
    
    <!-- Cancel Task -->
    <serviceTask id="cancelSubscription" name="Cancel Subscription" flowable:delegateExpression="${cancelSubscription}"/>
    <sequenceFlow id="flow14" sourceRef="cancelEvent" targetRef="cancelSubscription"/>
    
    <!-- End Event -->
    <endEvent id="endEvent" name="End"/>
    <sequenceFlow id="flow15" sourceRef="cancelSubscription" targetRef="endEvent"/>
    
  </process>
  
</definitions>
